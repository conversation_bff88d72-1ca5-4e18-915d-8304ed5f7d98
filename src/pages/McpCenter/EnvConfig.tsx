import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space, 
  Table, 
  Modal, 
  message,
  Typography,
  Alert,
  Tabs,
  Tag,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import type { McpEnvironmentConfig, EnvironmentType } from '@/types/mcp';
import { 
  getMcpEnvironmentConfigs, 
  saveMcpEnvironmentConfig, 
  deleteMcpEnvironmentConfig 
} from '@/services/mcp';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const EnvConfig: React.FC = () => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [configs, setConfigs] = useState<McpEnvironmentConfig[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<McpEnvironmentConfig | null>(null);
  const [activeEnvironment, setActiveEnvironment] = useState<EnvironmentType>('development');
  const [visiblePasswords, setVisiblePasswords] = useState<Set<string>>(new Set());

  // 环境类型选项
  const environmentOptions = [
    { label: '开发环境', value: 'development' },
    { label: '测试环境', value: 'testing' },
    { label: '生产环境', value: 'production' }
  ];

  // 加载配置数据
  const loadConfigs = async () => {
    setLoading(true);
    try {
      const response = await getMcpEnvironmentConfigs();
      setConfigs(response.data);
    } catch (error) {
      console.error('Failed to load environment configs:', error);
      message.error('加载环境配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // 处理保存配置
  const handleSave = async (values: any) => {
    try {
      const configData = {
        ...values,
        id: editingConfig?.id || undefined,
        environment: activeEnvironment,
        variables: values.variables || {}
      };
      
      await saveMcpEnvironmentConfig(configData);
      message.success('保存成功');
      setModalVisible(false);
      setEditingConfig(null);
      form.resetFields();
      loadConfigs();
    } catch (error) {
      console.error('Failed to save config:', error);
      message.error('保存失败');
    }
  };

  // 处理删除配置
  const handleDelete = async (id: string) => {
    try {
      await deleteMcpEnvironmentConfig(id);
      message.success('删除成功');
      loadConfigs();
    } catch (error) {
      console.error('Failed to delete config:', error);
      message.error('删除失败');
    }
  };

  // 处理编辑配置
  const handleEdit = (config: McpEnvironmentConfig) => {
    setEditingConfig(config);
    form.setFieldsValue({
      serviceId: config.serviceId,
      variables: config.variables
    });
    setActiveEnvironment(config.environment);
    setModalVisible(true);
  };

  // 切换密码可见性
  const togglePasswordVisibility = (key: string) => {
    const newVisible = new Set(visiblePasswords);
    if (newVisible.has(key)) {
      newVisible.delete(key);
    } else {
      newVisible.add(key);
    }
    setVisiblePasswords(newVisible);
  };

  // 渲染敏感信息
  const renderSensitiveValue = (value: string, key: string) => {
    const isVisible = visiblePasswords.has(key);
    return (
      <Space>
        <Text code style={{ fontFamily: 'monospace' }}>
          {isVisible ? value : '••••••••'}
        </Text>
        <Button
          type="text"
          size="small"
          icon={isVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          onClick={() => togglePasswordVisibility(key)}
        />
      </Space>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '服务ID',
      dataIndex: 'serviceId',
      key: 'serviceId',
    },
    {
      title: '环境',
      dataIndex: 'environment',
      key: 'environment',
      render: (env: EnvironmentType) => {
        const colors = {
          development: 'blue',
          testing: 'orange',
          production: 'red'
        };
        const names = {
          development: '开发',
          testing: '测试',
          production: '生产'
        };
        return <Tag color={colors[env]}>{names[env]}</Tag>;
      },
    },
    {
      title: '变量数量',
      dataIndex: 'variables',
      key: 'variableCount',
      render: (variables: Record<string, string>) => Object.keys(variables).length,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: McpEnvironmentConfig) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 按环境分组的配置
  const configsByEnvironment = configs.reduce((acc, config) => {
    if (!acc[config.environment]) {
      acc[config.environment] = [];
    }
    acc[config.environment].push(config);
    return acc;
  }, {} as Record<EnvironmentType, McpEnvironmentConfig[]>);

  return (
    <PageContainer
      title={intl.formatMessage({ id: 'pages.mcpCenter.envConfig.title' })}
      subTitle={intl.formatMessage({ id: 'pages.mcpCenter.envConfig.description' })}
      extra={[
        <Button
          key="refresh"
          icon={<ReloadOutlined />}
          onClick={loadConfigs}
        >
          刷新
        </Button>,
        <Button
          key="add"
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingConfig(null);
            form.resetFields();
            setModalVisible(true);
          }}
        >
          新增配置
        </Button>
      ]}
    >
      <Alert
        message="环境配置说明"
        description="在这里管理不同环境下MCP服务的环境变量配置。请注意保护敏感信息如API密钥等。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Card>
        <Tabs defaultActiveKey="development">
          {environmentOptions.map(env => (
            <TabPane 
              tab={`${env.label} (${configsByEnvironment[env.value]?.length || 0})`} 
              key={env.value}
            >
              <Table
                columns={columns}
                dataSource={configsByEnvironment[env.value] || []}
                rowKey="id"
                loading={loading}
                pagination={{ pageSize: 10 }}
              />
            </TabPane>
          ))}
        </Tabs>
      </Card>

      {/* 配置编辑模态框 */}
      <Modal
        title={editingConfig ? '编辑环境配置' : '新增环境配置'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="serviceId"
            label="服务ID"
            rules={[{ required: true, message: '请输入服务ID' }]}
          >
            <Input placeholder="例如: kfc-ordering-service" />
          </Form.Item>

          <Form.Item label="环境">
            <Select
              value={activeEnvironment}
              onChange={setActiveEnvironment}
              style={{ width: '100%' }}
            >
              {environmentOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="环境变量">
            <Form.List name="variablesList">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                      <Form.Item
                        {...restField}
                        name={[name, 'key']}
                        rules={[{ required: true, message: '请输入变量名' }]}
                      >
                        <Input placeholder="变量名" />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'value']}
                        rules={[{ required: true, message: '请输入变量值' }]}
                      >
                        <Input.Password placeholder="变量值" />
                      </Form.Item>
                      <Button type="text" danger onClick={() => remove(name)}>
                        删除
                      </Button>
                    </Space>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                      添加环境变量
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                保存
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default EnvConfig;
