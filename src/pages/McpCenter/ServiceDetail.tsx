import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Tag, 
  Space, 
  Button, 
  Rate, 
  Divider,
  Tabs,
  Alert,
  Descriptions,
  Avatar,
  Spin,
  message
} from 'antd';
import { 
  DownloadOutlined, 
  StarOutlined, 
  LinkOutlined,
  GithubOutlined,
  HomeOutlined,
  ApiOutlined,
  RocketOutlined,
  BulbOutlined,
  AppstoreOutlined,
  ToolOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CopyOutlined
} from '@ant-design/icons';
import { useParams, history } from '@umijs/max';
import type { McpServiceDetail } from '@/types/mcp';
import { getMcpServiceDetail } from '@/services/mcp';
import CodeBlock from './components/CodeBlock';
import EnvironmentVariables from './components/EnvironmentVariables';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const ServiceDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [service, setService] = useState<McpServiceDetail | null>(null);

  // 分类图标映射
  const categoryIcons = {
    ordering: <RocketOutlined style={{ color: '#1890ff' }} />,
    marketing: <BulbOutlined style={{ color: '#52c41a' }} />,
    knowledge: <AppstoreOutlined style={{ color: '#722ed1' }} />,
    tools: <ToolOutlined style={{ color: '#fa8c16' }}/>
  };

  // 分类名称映射
  const categoryNames = {
    ordering: '点餐相关',
    marketing: '营销相关',
    knowledge: '知识服务',
    tools: '便捷功能'
  };

  // 状态颜色映射
  const statusColors = {
    active: 'success',
    inactive: 'default',
    maintenance: 'warning'
  };

  // 状态名称映射
  const statusNames = {
    active: '运行中',
    inactive: '已停用',
    maintenance: '维护中'
  };

  // 加载服务详情
  const loadServiceDetail = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const response = await getMcpServiceDetail(id);
      setService(response.data);
    } catch (error) {
      console.error('Failed to load service detail:', error);
      message.error('加载服务详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadServiceDetail();
  }, [id]);

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    });
  };

  // 处理安装
  const handleInstall = () => {
    if (service?.installCommand) {
      copyToClipboard(service.installCommand);
    }
  };

  if (loading) {
    return (
      <PageContainer>
        <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '50px' }} />
      </PageContainer>
    );
  }

  if (!service) {
    return (
      <PageContainer>
        <Alert
          message="服务不存在"
          description="请检查服务ID是否正确"
          type="error"
          showIcon
        />
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title={service.displayName}
      subTitle={service.description}
      onBack={() => history.goBack()}
      extra={[
        <Button key="install" type="primary" icon={<DownloadOutlined />} onClick={handleInstall}>
          安装服务
        </Button>
      ]}
    >
      <Row gutter={[24, 24]}>
        {/* 左侧主要内容 */}
        <Col xs={24} lg={16}>
          <Card>
            <Tabs defaultActiveKey="readme">
              <TabPane tab="README" key="readme">
                <div dangerouslySetInnerHTML={{ __html: service.readme }} />
              </TabPane>
              
              <TabPane tab="安装说明" key="installation">
                <Space direction="vertical" size={16} style={{ width: '100%' }}>
                  <Alert
                    message="安装命令"
                    description={
                      <CodeBlock 
                        code={service.installCommand || 'npm install @kfc/mcp-' + service.name}
                        language="bash"
                        showCopy
                      />
                    }
                    type="info"
                    showIcon
                  />
                  
                  <div dangerouslySetInnerHTML={{ __html: service.installInstructions }} />
                </Space>
              </TabPane>
              
              <TabPane tab="配置指南" key="configuration">
                <Space direction="vertical" size={16} style={{ width: '100%' }}>
                  {service.configExample && (
                    <Alert
                      message="配置示例"
                      description={
                        <CodeBlock 
                          code={service.configExample}
                          language="json"
                          showCopy
                        />
                      }
                      type="info"
                      showIcon
                    />
                  )}
                  
                  <div dangerouslySetInnerHTML={{ __html: service.configurationGuide }} />
                </Space>
              </TabPane>
              
              <TabPane tab="环境变量" key="environment">
                <EnvironmentVariables variables={service.environmentVariables} />
              </TabPane>
              
              {service.examples.length > 0 && (
                <TabPane tab="示例代码" key="examples">
                  <Space direction="vertical" size={16} style={{ width: '100%' }}>
                    {service.examples.map((example, index) => (
                      <Card key={example.id} size="small" title={example.title}>
                        <Paragraph>{example.description}</Paragraph>
                        <CodeBlock 
                          code={example.code}
                          language={example.language}
                          showCopy
                        />
                      </Card>
                    ))}
                  </Space>
                </TabPane>
              )}
              
              <TabPane tab="更新日志" key="changelog">
                <div dangerouslySetInnerHTML={{ __html: service.changelog }} />
              </TabPane>
            </Tabs>
          </Card>
        </Col>
        
        {/* 右侧信息面板 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size={16} style={{ width: '100%' }}>
            {/* 基本信息 */}
            <Card title="基本信息" size="small">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="版本">
                  <Tag color="blue">v{service.version}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={statusColors[service.status]}>
                    {statusNames[service.status]}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="分类">
                  <Space>
                    {categoryIcons[service.category]}
                    {categoryNames[service.category]}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="作者">
                  <Space>
                    <UserOutlined />
                    {service.author}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="许可证">
                  {service.license || 'MIT'}
                </Descriptions.Item>
                <Descriptions.Item label="最后更新">
                  <Space>
                    <ClockCircleOutlined />
                    {service.lastUpdate}
                  </Space>
                </Descriptions.Item>
              </Descriptions>
            </Card>
            
            {/* 统计信息 */}
            <Card title="统计信息" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>评分</Text>
                  <Space>
                    <Rate disabled value={service.rating} style={{ fontSize: '12px' }} />
                    <Text>{service.rating.toFixed(1)}</Text>
                  </Space>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>下载量</Text>
                  <Text>{service.downloads.toLocaleString()}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>评价数</Text>
                  <Text>{service.ratingCount}</Text>
                </div>
              </Space>
            </Card>
            
            {/* 标签 */}
            <Card title="标签" size="small">
              <Space wrap>
                {service.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>
            </Card>
            
            {/* 链接 */}
            <Card title="相关链接" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                {service.homepage && (
                  <Button 
                    type="link" 
                    icon={<HomeOutlined />} 
                    href={service.homepage}
                    target="_blank"
                    style={{ padding: 0 }}
                  >
                    主页
                  </Button>
                )}
                {service.repository && (
                  <Button 
                    type="link" 
                    icon={<GithubOutlined />} 
                    href={service.repository}
                    target="_blank"
                    style={{ padding: 0 }}
                  >
                    源码仓库
                  </Button>
                )}
                {service.documentation && (
                  <Button 
                    type="link" 
                    icon={<LinkOutlined />} 
                    href={service.documentation}
                    target="_blank"
                    style={{ padding: 0 }}
                  >
                    文档
                  </Button>
                )}
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default ServiceDetail;
