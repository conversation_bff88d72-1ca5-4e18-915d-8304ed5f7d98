import React from 'react';
import { Button, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';

interface CodeBlockProps {
  code: string;
  language?: string;
  showCopy?: boolean;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ code, language = 'text', showCopy = false }) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(code).then(() => {
      message.success('已复制到剪贴板');
    });
  };

  return (
    <div style={{ position: 'relative' }}>
      <pre
        style={{
          background: '#f6f8fa',
          border: '1px solid #e1e4e8',
          borderRadius: '6px',
          padding: '16px',
          overflow: 'auto',
          fontSize: '14px',
          lineHeight: '1.45',
          fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
          margin: 0
        }}
      >
        <code>{code}</code>
      </pre>
      {showCopy && (
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={copyToClipboard}
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            background: 'rgba(255, 255, 255, 0.8)',
            border: '1px solid #e1e4e8'
          }}
        />
      )}
    </div>
  );
};

export default CodeBlock;
