import React from 'react';
import { Table, Tag, Typography, Space, Tooltip } from 'antd';
import { InfoCircleOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import type { McpEnvironmentVariable } from '@/types/mcp';

const { Text } = Typography;

interface EnvironmentVariablesProps {
  variables: McpEnvironmentVariable[];
}

const EnvironmentVariables: React.FC<EnvironmentVariablesProps> = ({ variables }) => {
  const columns = [
    {
      title: '变量名',
      dataIndex: 'key',
      key: 'key',
      render: (key: string, record: McpEnvironmentVariable) => (
        <Space>
          <Text code>{key}</Text>
          {record.sensitive && (
            <Tooltip title="敏感信息">
              <EyeInvisibleOutlined style={{ color: '#faad14' }} />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '必需',
      dataIndex: 'required',
      key: 'required',
      render: (required: boolean) => (
        <Tag color={required ? 'red' : 'default'}>
          {required ? '必需' : '可选'}
        </Tag>
      ),
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      render: (defaultValue?: string) => (
        defaultValue ? <Text code>{defaultValue}</Text> : <Text type="secondary">-</Text>
      ),
    },
    {
      title: '环境',
      dataIndex: 'environments',
      key: 'environments',
      render: (environments: string[]) => (
        <Space wrap>
          {environments.map(env => (
            <Tag key={env} size="small">
              {env === 'development' ? '开发' : env === 'testing' ? '测试' : '生产'}
            </Tag>
          ))}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <InfoCircleOutlined style={{ color: '#1890ff' }} />
          <Text type="secondary">
            以下是该MCP服务所需的环境变量配置。敏感信息请妥善保管。
          </Text>
        </Space>
      </div>
      
      <Table
        columns={columns}
        dataSource={variables}
        rowKey="key"
        size="small"
        pagination={false}
        scroll={{ x: 800 }}
      />
    </div>
  );
};

export default EnvironmentVariables;
