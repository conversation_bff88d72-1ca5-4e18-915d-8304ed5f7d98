/**
 * MCP中心相关的类型定义
 */

// MCP服务状态
export type McpServiceStatus = 'active' | 'inactive' | 'maintenance';

// MCP服务分类
export type McpServiceCategory = 'ordering' | 'marketing' | 'knowledge' | 'tools';

// 环境类型
export type EnvironmentType = 'development' | 'testing' | 'production';

// MCP服务基本信息
export interface McpService {
  id: string;
  name: string;
  displayName: string;
  description: string;
  category: McpServiceCategory;
  status: McpServiceStatus;
  version: string;
  author: string;
  tags: string[];
  icon?: string;
  lastUpdate: string;
  downloads: number;
  rating: number;
  ratingCount: number;
  installCommand?: string;
  configExample?: string;
  documentation?: string;
  repository?: string;
  homepage?: string;
  license?: string;
  dependencies?: string[];
  environments?: EnvironmentType[];
}

// MCP服务详细信息
export interface McpServiceDetail extends McpService {
  readme: string;
  changelog: string;
  installInstructions: string;
  configurationGuide: string;
  apiReference?: string;
  examples: McpServiceExample[];
  environmentVariables: McpEnvironmentVariable[];
}

// MCP服务示例
export interface McpServiceExample {
  id: string;
  title: string;
  description: string;
  code: string;
  language: string;
}

// 环境变量定义
export interface McpEnvironmentVariable {
  key: string;
  description: string;
  required: boolean;
  defaultValue?: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  sensitive: boolean; // 是否为敏感信息（如API Key）
  environments: EnvironmentType[];
}

// 环境配置
export interface McpEnvironmentConfig {
  id: string;
  serviceId: string;
  environment: EnvironmentType;
  variables: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

// 分类信息
export interface McpCategory {
  key: McpServiceCategory;
  name: string;
  description: string;
  icon: string;
  color: string;
  count: number;
}

// 搜索和筛选参数
export interface McpSearchParams {
  keyword?: string;
  category?: McpServiceCategory;
  status?: McpServiceStatus;
  tags?: string[];
  sortBy?: 'name' | 'downloads' | 'rating' | 'lastUpdate';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

// 搜索结果
export interface McpSearchResult {
  services: McpService[];
  total: number;
  page: number;
  pageSize: number;
  categories: McpCategory[];
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 统计信息
export interface McpStatistics {
  totalServices: number;
  activeServices: number;
  totalDownloads: number;
  categoryCounts: Record<McpServiceCategory, number>;
  recentUpdates: McpService[];
  popularServices: McpService[];
}
